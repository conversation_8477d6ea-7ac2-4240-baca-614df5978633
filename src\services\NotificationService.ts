import { EventEmitter } from 'events';

export interface Notification {
  id: string;
  type: 'like' | 'comment' | 'share' | 'friend_request' | 'friend_accepted' | 'tag' | 'event' | 'birthday' | 'job' | 'group' | 'video_call' | 'live_stream' | 'memory' | 'message' | 'post' | 'story';
  title: string;
  message: string;
  timestamp: Date;
  isRead: boolean;
  isImportant: boolean;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  category: 'social' | 'system' | 'promotional' | 'security';
  actionUrl?: string;
  imageUrl?: string;
  user?: {
    id: string;
    name: string;
    avatar?: string;
  };
  metadata?: Record<string, unknown>;
  expiresAt?: Date;
  soundEnabled?: boolean;
  vibrationEnabled?: boolean;
}

export interface NotificationSettings {
  enabled: boolean;
  sound: boolean;
  vibration: boolean;
  desktop: boolean;
  email: boolean;
  sms: boolean;
  categories: {
    social: boolean;
    system: boolean;
    promotional: boolean;
    security: boolean;
  };
  quietHours: {
    enabled: boolean;
    start: string; // HH:MM format
    end: string;   // HH:MM format
  };
  frequency: 'immediate' | 'batched' | 'daily';
}

class NotificationService extends EventEmitter {
  private static instance: NotificationService;
  private notifications: Map<string, Notification> = new Map();
  private ws: WebSocket | null = null;
  private settings: NotificationSettings;
  private isConnected = false;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000;
  private currentUserId = 'current-user';
  private notificationQueue: Notification[] = [];
  private batchTimer: NodeJS.Timeout | null = null;

  // Audio context for notification sounds
  private audioContext: AudioContext | null = null;
  private notificationSounds: Map<string, AudioBuffer> = new Map();

  static getInstance(): NotificationService {
    if (!NotificationService.instance) {
      NotificationService.instance = new NotificationService();
    }
    return NotificationService.instance;
  }

  constructor() {
    super();
    
    // Default settings
    this.settings = {
      enabled: true,
      sound: true,
      vibration: true,
      desktop: true,
      email: false,
      sms: false,
      categories: {
        social: true,
        system: true,
        promotional: false,
        security: true
      },
      quietHours: {
        enabled: false,
        start: '22:00',
        end: '08:00'
      },
      frequency: 'immediate'
    };

    this.loadSettings();
    this.initializeWebSocket();
    this.initializeAudio();
    this.requestNotificationPermission();
  }

  private loadSettings() {
    const saved = localStorage.getItem('notification-settings');
    if (saved) {
      this.settings = { ...this.settings, ...JSON.parse(saved) };
    }
  }

  private saveSettings() {
    localStorage.setItem('notification-settings', JSON.stringify(this.settings));
  }

  private async requestNotificationPermission(): Promise<boolean> {
    if (!('Notification' in window)) {
      console.warn('This browser does not support notifications');
      return false;
    }

    if (Notification.permission === 'granted') {
      return true;
    }

    if (Notification.permission !== 'denied') {
      const permission = await Notification.requestPermission();
      return permission === 'granted';
    }

    return false;
  }

  private initializeWebSocket() {
    try {
      this.ws = new WebSocket('ws://localhost:8080/ws/notifications');
      
      this.ws.onopen = () => {
        console.log('Notification WebSocket connected');
        this.isConnected = true;
        this.reconnectAttempts = 0;
        this.emit('connected');
        
        // Send authentication
        this.send({
          type: 'auth',
          userId: this.currentUserId
        });
      };

      this.ws.onmessage = (event) => {
        const data = JSON.parse(event.data);
        this.handleMessage(data);
      };

      this.ws.onclose = () => {
        console.log('Notification WebSocket disconnected');
        this.isConnected = false;
        this.emit('disconnected');
        this.attemptReconnect();
      };

      this.ws.onerror = (error) => {
        console.error('Notification WebSocket error:', error);
        this.emit('error', error);
      };
    } catch (error) {
      console.warn('WebSocket not available, using mock implementation');
      this.initializeMockNotifications();
    }
  }

  private attemptReconnect() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);
      
      setTimeout(() => {
        console.log(`Attempting to reconnect... (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
        this.initializeWebSocket();
      }, delay);
    }
  }

  private initializeMockNotifications() {
    // Simulate receiving notifications for demo
    setTimeout(() => {
      this.emit('connected');
      
      // Generate mock notifications periodically
      setInterval(() => {
        if (Math.random() > 0.7) { // 30% chance every 10 seconds
          this.generateMockNotification();
        }
      }, 10000);
    }, 1000);
  }

  private generateMockNotification() {
    const types = ['like', 'comment', 'friend_request', 'message', 'tag'] as const;
    const users = [
      { id: '1', name: 'Alice Johnson', avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=alice' },
      { id: '2', name: 'Bob Smith', avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=bob' },
      { id: '3', name: 'Carol Davis', avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=carol' }
    ];

    const type = types[Math.floor(Math.random() * types.length)];
    const user = users[Math.floor(Math.random() * users.length)];

    const notification: Notification = {
      id: `notif-${Date.now()}`,
      type,
      title: this.getNotificationTitle(type, user.name),
      message: this.getNotificationMessage(type, user.name),
      timestamp: new Date(),
      isRead: false,
      isImportant: type === 'friend_request' || type === 'message',
      priority: type === 'message' ? 'high' : 'medium',
      category: 'social',
      user,
      soundEnabled: true,
      vibrationEnabled: true
    };

    this.addNotification(notification);
  }

  private getNotificationTitle(type: string, userName: string): string {
    switch (type) {
      case 'like': return 'New Like';
      case 'comment': return 'New Comment';
      case 'friend_request': return 'Friend Request';
      case 'message': return 'New Message';
      case 'tag': return 'You were tagged';
      default: return 'New Notification';
    }
  }

  private getNotificationMessage(type: string, userName: string): string {
    switch (type) {
      case 'like': return `${userName} liked your post`;
      case 'comment': return `${userName} commented on your post`;
      case 'friend_request': return `${userName} sent you a friend request`;
      case 'message': return `${userName} sent you a message`;
      case 'tag': return `${userName} tagged you in a post`;
      default: return `${userName} interacted with your content`;
    }
  }

  private async initializeAudio() {
    try {
      this.audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
      
      // Load notification sounds
      await this.loadNotificationSounds();
    } catch (error) {
      console.warn('Audio context not available:', error);
    }
  }

  private async loadNotificationSounds() {
    const sounds = {
      default: '/sounds/notification.mp3',
      message: '/sounds/message.mp3',
      call: '/sounds/call.mp3',
      urgent: '/sounds/urgent.mp3'
    };

    for (const [name, url] of Object.entries(sounds)) {
      try {
        const response = await fetch(url);
        const arrayBuffer = await response.arrayBuffer();
        const audioBuffer = await this.audioContext!.decodeAudioData(arrayBuffer);
        this.notificationSounds.set(name, audioBuffer);
      } catch (error) {
        console.warn(`Failed to load sound ${name}:`, error);
      }
    }
  }

  private handleMessage(data: any) {
    switch (data.type) {
      case 'notification':
        this.addNotification(data.notification);
        break;
      case 'notification_read':
        this.markAsRead(data.notificationId);
        break;
      case 'notification_deleted':
        this.deleteNotification(data.notificationId);
        break;
      case 'bulk_read':
        this.markAllAsRead();
        break;
    }
  }

  private send(data: any) {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(data));
    }
  }

  private isInQuietHours(): boolean {
    if (!this.settings.quietHours.enabled) return false;

    const now = new Date();
    const currentTime = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`;
    
    const start = this.settings.quietHours.start;
    const end = this.settings.quietHours.end;

    if (start <= end) {
      return currentTime >= start && currentTime <= end;
    } else {
      return currentTime >= start || currentTime <= end;
    }
  }

  private shouldShowNotification(notification: Notification): boolean {
    if (!this.settings.enabled) return false;
    if (!this.settings.categories[notification.category]) return false;
    if (this.isInQuietHours() && notification.priority !== 'urgent') return false;
    
    return true;
  }

  private async playNotificationSound(notification: Notification) {
    if (!this.settings.sound || !notification.soundEnabled || !this.audioContext) return;

    const soundName = notification.priority === 'urgent' ? 'urgent' : 
                     notification.type === 'message' ? 'message' : 'default';
    
    const audioBuffer = this.notificationSounds.get(soundName) || this.notificationSounds.get('default');
    
    if (audioBuffer) {
      const source = this.audioContext.createBufferSource();
      source.buffer = audioBuffer;
      source.connect(this.audioContext.destination);
      source.start();
    }
  }

  private triggerVibration(notification: Notification) {
    if (!this.settings.vibration || !notification.vibrationEnabled || !navigator.vibrate) return;

    const pattern = notification.priority === 'urgent' ? [200, 100, 200, 100, 200] : [200];
    navigator.vibrate(pattern);
  }

  private async showDesktopNotification(notification: Notification) {
    if (!this.settings.desktop || !('Notification' in window)) return;

    const hasPermission = await this.requestNotificationPermission();
    if (!hasPermission) return;

    const options: NotificationOptions = {
      body: notification.message,
      icon: notification.user?.avatar || '/favicon.ico',
      image: notification.imageUrl,
      badge: '/favicon.ico',
      tag: notification.id,
      requireInteraction: notification.priority === 'urgent',
      silent: !notification.soundEnabled,
      vibrate: notification.vibrationEnabled ? [200] : undefined,
      data: {
        notificationId: notification.id,
        actionUrl: notification.actionUrl
      }
    };

    const desktopNotification = new Notification(notification.title, options);
    
    desktopNotification.onclick = () => {
      window.focus();
      if (notification.actionUrl) {
        window.location.href = notification.actionUrl;
      }
      this.markAsRead(notification.id);
      desktopNotification.close();
    };

    // Auto-close after 5 seconds for non-urgent notifications
    if (notification.priority !== 'urgent') {
      setTimeout(() => {
        desktopNotification.close();
      }, 5000);
    }
  }

  // Public API
  addNotification(notification: Notification) {
    this.notifications.set(notification.id, notification);
    
    if (this.shouldShowNotification(notification)) {
      if (this.settings.frequency === 'immediate') {
        this.displayNotification(notification);
      } else {
        this.queueNotification(notification);
      }
    }

    this.emit('notification_added', notification);
    this.persistNotifications();
  }

  private async displayNotification(notification: Notification) {
    await this.playNotificationSound(notification);
    this.triggerVibration(notification);
    await this.showDesktopNotification(notification);
    this.emit('notification_displayed', notification);
  }

  private queueNotification(notification: Notification) {
    this.notificationQueue.push(notification);
    
    if (this.settings.frequency === 'batched' && !this.batchTimer) {
      this.batchTimer = setTimeout(() => {
        this.processBatchedNotifications();
      }, 30000); // Process batch every 30 seconds
    }
  }

  private async processBatchedNotifications() {
    if (this.notificationQueue.length === 0) return;

    const notifications = [...this.notificationQueue];
    this.notificationQueue = [];
    this.batchTimer = null;

    // Show summary notification for multiple notifications
    if (notifications.length > 1) {
      const summaryNotification: Notification = {
        id: `batch-${Date.now()}`,
        type: 'system',
        title: 'New Notifications',
        message: `You have ${notifications.length} new notifications`,
        timestamp: new Date(),
        isRead: false,
        isImportant: notifications.some(n => n.isImportant),
        priority: 'medium',
        category: 'system',
        soundEnabled: true,
        vibrationEnabled: true
      };

      await this.displayNotification(summaryNotification);
    } else {
      await this.displayNotification(notifications[0]);
    }

    this.emit('batch_processed', notifications);
  }

  markAsRead(notificationId: string) {
    const notification = this.notifications.get(notificationId);
    if (notification && !notification.isRead) {
      notification.isRead = true;
      this.notifications.set(notificationId, notification);
      this.emit('notification_read', notification);
      this.persistNotifications();
      
      // Sync with server
      this.send({
        type: 'mark_read',
        notificationId
      });
    }
  }

  markAllAsRead() {
    let hasChanges = false;
    for (const [id, notification] of this.notifications) {
      if (!notification.isRead) {
        notification.isRead = true;
        this.notifications.set(id, notification);
        hasChanges = true;
      }
    }

    if (hasChanges) {
      this.emit('all_notifications_read');
      this.persistNotifications();
      
      // Sync with server
      this.send({
        type: 'mark_all_read'
      });
    }
  }

  deleteNotification(notificationId: string) {
    const notification = this.notifications.get(notificationId);
    if (notification) {
      this.notifications.delete(notificationId);
      this.emit('notification_deleted', notification);
      this.persistNotifications();
      
      // Sync with server
      this.send({
        type: 'delete_notification',
        notificationId
      });
    }
  }

  clearAllNotifications() {
    this.notifications.clear();
    this.emit('all_notifications_cleared');
    this.persistNotifications();
    
    // Sync with server
    this.send({
      type: 'clear_all'
    });
  }

  getNotifications(): Notification[] {
    return Array.from(this.notifications.values())
      .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
  }

  getUnreadCount(): number {
    return Array.from(this.notifications.values())
      .filter(n => !n.isRead).length;
  }

  getNotificationsByCategory(category: string): Notification[] {
    return this.getNotifications().filter(n => n.category === category);
  }

  updateSettings(newSettings: Partial<NotificationSettings>) {
    this.settings = { ...this.settings, ...newSettings };
    this.saveSettings();
    this.emit('settings_updated', this.settings);
  }

  getSettings(): NotificationSettings {
    return { ...this.settings };
  }

  private persistNotifications() {
    const notifications = Array.from(this.notifications.values());
    localStorage.setItem('notifications', JSON.stringify(notifications));
  }

  private loadPersistedNotifications() {
    const saved = localStorage.getItem('notifications');
    if (saved) {
      const notifications: Notification[] = JSON.parse(saved);
      notifications.forEach(n => {
        n.timestamp = new Date(n.timestamp);
        if (n.expiresAt) n.expiresAt = new Date(n.expiresAt);
        this.notifications.set(n.id, n);
      });
    }
  }

  // Initialize persisted notifications
  init() {
    this.loadPersistedNotifications();
    this.cleanupExpiredNotifications();
  }

  private cleanupExpiredNotifications() {
    const now = new Date();
    for (const [id, notification] of this.notifications) {
      if (notification.expiresAt && notification.expiresAt < now) {
        this.notifications.delete(id);
      }
    }
    this.persistNotifications();
  }

  // Cleanup
  destroy() {
    if (this.ws) {
      this.ws.close();
    }
    if (this.batchTimer) {
      clearTimeout(this.batchTimer);
    }
    if (this.audioContext) {
      this.audioContext.close();
    }
    this.removeAllListeners();
  }
}

export default NotificationService;
