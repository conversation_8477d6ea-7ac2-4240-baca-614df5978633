import React, { useState, useEffect, useCallback, useMemo } from 'react';
import {
  Search,
  Filter,
  MapPin,
  Heart,
  Share2,
  MessageCircle,
  Phone,
  Star,
  Grid,
  List,
  ChevronDown,
  Plus,
  X,
  Camera,
  DollarSign,
  Package,
  Truck,
  Shield,
  Clock,
  User,
  Store,
  SlidersHorizontal
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Slider } from '@/components/ui/slider';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { formatDistanceToNow } from 'date-fns';
import { toast } from 'sonner';
import { motion, AnimatePresence } from 'framer-motion';
import SocialFeaturesService, { MarketplaceListing, User, Location } from '@/services/SocialFeaturesService';
import OptimizedImage from './OptimizedImage';

interface MarketplaceFilters {
  category: string;
  condition: string;
  priceMin: number;
  priceMax: number;
  location: string;
  radius: number;
  sortBy: 'recent' | 'price_low' | 'price_high' | 'distance';
  deliveryOptions: string[];
}

interface FacebookMarketplaceProps {
  className?: string;
  userId?: string;
}

const categories = [
  { id: 'all', name: 'All Categories', icon: '🏪' },
  { id: 'vehicles', name: 'Vehicles', icon: '🚗' },
  { id: 'electronics', name: 'Electronics', icon: '📱' },
  { id: 'home', name: 'Home & Garden', icon: '🏠' },
  { id: 'clothing', name: 'Clothing & Accessories', icon: '👕' },
  { id: 'sports', name: 'Sports & Outdoors', icon: '⚽' },
  { id: 'books', name: 'Books & Media', icon: '📚' },
  { id: 'toys', name: 'Toys & Games', icon: '🧸' },
  { id: 'furniture', name: 'Furniture', icon: '🪑' },
  { id: 'tools', name: 'Tools & Equipment', icon: '🔧' },
  { id: 'other', name: 'Other', icon: '📦' }
];

const conditions = [
  { id: 'all', name: 'All Conditions' },
  { id: 'new', name: 'New' },
  { id: 'used', name: 'Used - Like New' },
  { id: 'good', name: 'Used - Good' },
  { id: 'fair', name: 'Used - Fair' },
  { id: 'refurbished', name: 'Refurbished' }
];

const FacebookMarketplace: React.FC<FacebookMarketplaceProps> = ({
  className = '',
  userId
}) => {
  const [listings, setListings] = useState<MarketplaceListing[]>([]);
  const [filteredListings, setFilteredListings] = useState<MarketplaceListing[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [selectedListing, setSelectedListing] = useState<MarketplaceListing | null>(null);
  const [showFilters, setShowFilters] = useState(false);
  const [showCreateListing, setShowCreateListing] = useState(false);
  const [currentUser, setCurrentUser] = useState<User | null>(null);
  const [savedListings, setSavedListings] = useState<string[]>([]);

  const [filters, setFilters] = useState<MarketplaceFilters>({
    category: 'all',
    condition: 'all',
    priceMin: 0,
    priceMax: 10000,
    location: '',
    radius: 25,
    sortBy: 'recent',
    deliveryOptions: []
  });

  const [newListing, setNewListing] = useState({
    title: '',
    description: '',
    price: '',
    category: '',
    condition: 'used',
    photos: [] as string[],
    location: null as Location | null,
    deliveryOptions: ['pickup'] as string[],
    contactInfo: {
      phone: '',
      email: '',
      messenger: true
    }
  });

  const socialService = useMemo(() => SocialFeaturesService.getInstance(), []);

  useEffect(() => {
    const loadMarketplace = async () => {
      setLoading(true);
      try {
        const user = socialService.getCurrentUser();
        setCurrentUser(user);

        const allListings = socialService.getMarketplaceListings();
        setListings(allListings);
        setFilteredListings(allListings);

        // Load saved listings from localStorage
        const saved = localStorage.getItem('saved-listings');
        if (saved) {
          setSavedListings(JSON.parse(saved));
        }
      } catch (error) {
        console.error('Error loading marketplace:', error);
        toast.error('Failed to load marketplace');
      } finally {
        setLoading(false);
      }
    };

    loadMarketplace();
  }, [socialService]);

  // Apply filters and search
  useEffect(() => {
    let filtered = [...listings];

    // Search filter
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(listing =>
        listing.title.toLowerCase().includes(query) ||
        listing.description.toLowerCase().includes(query) ||
        listing.category.toLowerCase().includes(query)
      );
    }

    // Category filter
    if (filters.category !== 'all') {
      filtered = filtered.filter(listing => listing.category === filters.category);
    }

    // Condition filter
    if (filters.condition !== 'all') {
      filtered = filtered.filter(listing => listing.condition === filters.condition);
    }

    // Price filter
    filtered = filtered.filter(listing => 
      listing.price >= filters.priceMin && listing.price <= filters.priceMax
    );

    // Location filter (mock implementation)
    if (filters.location) {
      const query = filters.location.toLowerCase();
      filtered = filtered.filter(listing =>
        listing.location.name.toLowerCase().includes(query) ||
        listing.location.address?.toLowerCase().includes(query)
      );
    }

    // Delivery options filter
    if (filters.deliveryOptions.length > 0) {
      filtered = filtered.filter(listing =>
        filters.deliveryOptions.some(option => listing.deliveryOptions.includes(option))
      );
    }

    // Sort
    switch (filters.sortBy) {
      case 'price_low':
        filtered.sort((a, b) => a.price - b.price);
        break;
      case 'price_high':
        filtered.sort((a, b) => b.price - a.price);
        break;
      case 'recent':
      default:
        filtered.sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());
        break;
    }

    setFilteredListings(filtered);
  }, [listings, searchQuery, filters]);

  const handleSaveListing = useCallback((listingId: string) => {
    const newSaved = savedListings.includes(listingId)
      ? savedListings.filter(id => id !== listingId)
      : [...savedListings, listingId];
    
    setSavedListings(newSaved);
    localStorage.setItem('saved-listings', JSON.stringify(newSaved));
    
    socialService.saveMarketplaceListing(listingId);
    toast.success(savedListings.includes(listingId) ? 'Listing unsaved' : 'Listing saved');
  }, [savedListings, socialService]);

  const handleCreateListing = useCallback(async () => {
    try {
      if (!newListing.title || !newListing.price || !newListing.category) {
        toast.error('Please fill in all required fields');
        return;
      }

      const listing = await socialService.createMarketplaceListing({
        title: newListing.title,
        description: newListing.description,
        price: parseFloat(newListing.price),
        category: newListing.category,
        condition: newListing.condition as 'new' | 'used' | 'refurbished',
        photos: newListing.photos,
        location: newListing.location!,
        deliveryOptions: newListing.deliveryOptions,
        contactInfo: newListing.contactInfo
      });

      setListings(prev => [listing, ...prev]);
      setShowCreateListing(false);
      
      // Reset form
      setNewListing({
        title: '',
        description: '',
        price: '',
        category: '',
        condition: 'used',
        photos: [],
        location: null,
        deliveryOptions: ['pickup'],
        contactInfo: {
          phone: '',
          email: '',
          messenger: true
        }
      });

      toast.success('Listing created successfully');
    } catch (error) {
      console.error('Error creating listing:', error);
      toast.error('Failed to create listing');
    }
  }, [newListing, socialService]);

  const handleContactSeller = useCallback((listing: MarketplaceListing, method: 'message' | 'phone' | 'email') => {
    switch (method) {
      case 'message':
        toast.success('Opening message conversation...');
        break;
      case 'phone':
        if (listing.contactInfo.phone) {
          window.open(`tel:${listing.contactInfo.phone}`);
        }
        break;
      case 'email':
        if (listing.contactInfo.email) {
          window.open(`mailto:${listing.contactInfo.email}`);
        }
        break;
    }
  }, []);

  const renderListingCard = useCallback((listing: MarketplaceListing) => {
    const isSaved = savedListings.includes(listing.id);
    
    return (
      <motion.div
        key={listing.id}
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.95 }}
        transition={{ duration: 0.2 }}
      >
        <Card className="group hover:shadow-lg transition-all duration-200 cursor-pointer overflow-hidden">
          <div className="relative" onClick={() => setSelectedListing(listing)}>
            {/* Image */}
            <div className="aspect-square relative overflow-hidden bg-gray-100">
              {listing.photos.length > 0 ? (
                <OptimizedImage
                  src={listing.photos[0]}
                  alt={listing.title}
                  className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                />
              ) : (
                <div className="w-full h-full flex items-center justify-center text-gray-400">
                  <Package className="w-12 h-12" />
                </div>
              )}
              
              {/* Photo count indicator */}
              {listing.photos.length > 1 && (
                <div className="absolute top-2 right-2 bg-black/70 text-white text-xs px-2 py-1 rounded-full">
                  +{listing.photos.length - 1}
                </div>
              )}

              {/* Save button */}
              <Button
                variant="ghost"
                size="sm"
                className="absolute top-2 left-2 bg-white/90 hover:bg-white w-8 h-8 p-0"
                onClick={(e) => {
                  e.stopPropagation();
                  handleSaveListing(listing.id);
                }}
              >
                <Heart className={`w-4 h-4 ${isSaved ? 'fill-red-500 text-red-500' : 'text-gray-600'}`} />
              </Button>

              {/* Promoted badge */}
              {listing.isPromoted && (
                <Badge className="absolute bottom-2 left-2 bg-yellow-500 text-yellow-900">
                  Promoted
                </Badge>
              )}
            </div>

            {/* Content */}
            <CardContent className="p-3">
              <div className="space-y-2">
                {/* Price */}
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-bold text-gray-900 dark:text-gray-100">
                    ${listing.price.toLocaleString()}
                  </h3>
                  <Badge variant="outline" className="text-xs">
                    {listing.condition}
                  </Badge>
                </div>

                {/* Title */}
                <h4 className="font-medium text-sm text-gray-800 dark:text-gray-200 line-clamp-2">
                  {listing.title}
                </h4>

                {/* Location and time */}
                <div className="flex items-center justify-between text-xs text-gray-500">
                  <div className="flex items-center space-x-1">
                    <MapPin className="w-3 h-3" />
                    <span className="truncate">{listing.location.name}</span>
                  </div>
                  <span>{formatDistanceToNow(listing.createdAt, { addSuffix: true })}</span>
                </div>

                {/* Delivery options */}
                <div className="flex flex-wrap gap-1">
                  {listing.deliveryOptions.map(option => (
                    <Badge key={option} variant="secondary" className="text-xs">
                      {option === 'pickup' ? <MapPin className="w-3 h-3 mr-1" /> : <Truck className="w-3 h-3 mr-1" />}
                      {option}
                    </Badge>
                  ))}
                </div>
              </div>
            </CardContent>
          </div>
        </Card>
      </motion.div>
    );
  }, [savedListings, handleSaveListing]);

  const renderListingDetail = useCallback(() => {
    if (!selectedListing) return null;

    const isSaved = savedListings.includes(selectedListing.id);

    return (
      <Dialog open={!!selectedListing} onOpenChange={() => setSelectedListing(null)}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="sr-only">Listing Details</DialogTitle>
          </DialogHeader>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Images */}
            <div className="space-y-4">
              {selectedListing.photos.length > 0 ? (
                <div className="space-y-2">
                  <div className="aspect-square rounded-lg overflow-hidden bg-gray-100">
                    <OptimizedImage
                      src={selectedListing.photos[0]}
                      alt={selectedListing.title}
                      className="w-full h-full object-cover"
                    />
                  </div>
                  {selectedListing.photos.length > 1 && (
                    <div className="grid grid-cols-4 gap-2">
                      {selectedListing.photos.slice(1, 5).map((photo, index) => (
                        <div key={index} className="aspect-square rounded overflow-hidden bg-gray-100">
                          <OptimizedImage
                            src={photo}
                            alt={`${selectedListing.title} ${index + 2}`}
                            className="w-full h-full object-cover"
                          />
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              ) : (
                <div className="aspect-square rounded-lg bg-gray-100 flex items-center justify-center">
                  <Package className="w-16 h-16 text-gray-400" />
                </div>
              )}

              {/* Seller info */}
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center space-x-3">
                    <Avatar>
                      <AvatarImage src={selectedListing.seller.avatar} />
                      <AvatarFallback>{selectedListing.seller.name[0]}</AvatarFallback>
                    </Avatar>
                    <div className="flex-1">
                      <h3 className="font-semibold">{selectedListing.seller.name}</h3>
                      <p className="text-sm text-gray-500">
                        Joined {formatDistanceToNow(new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000), { addSuffix: true })}
                      </p>
                    </div>
                    <div className="flex items-center space-x-1 text-yellow-500">
                      <Star className="w-4 h-4 fill-current" />
                      <span className="text-sm">4.8</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Details */}
            <div className="space-y-6">
              {/* Header */}
              <div className="space-y-4">
                <div className="flex items-start justify-between">
                  <div>
                    <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                      {selectedListing.title}
                    </h1>
                    <p className="text-3xl font-bold text-green-600 mt-2">
                      ${selectedListing.price.toLocaleString()}
                    </p>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleSaveListing(selectedListing.id)}
                  >
                    <Heart className={`w-4 h-4 mr-2 ${isSaved ? 'fill-red-500 text-red-500' : ''}`} />
                    {isSaved ? 'Saved' : 'Save'}
                  </Button>
                </div>

                {/* Tags */}
                <div className="flex flex-wrap gap-2">
                  <Badge variant="outline">{selectedListing.condition}</Badge>
                  <Badge variant="outline">{selectedListing.category}</Badge>
                  {selectedListing.isPromoted && (
                    <Badge className="bg-yellow-500 text-yellow-900">Promoted</Badge>
                  )}
                </div>
              </div>

              {/* Description */}
              <div>
                <h3 className="font-semibold mb-2">Description</h3>
                <p className="text-gray-700 dark:text-gray-300 whitespace-pre-wrap">
                  {selectedListing.description}
                </p>
              </div>

              {/* Details */}
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <span className="text-sm text-gray-500">Condition</span>
                  <p className="font-medium capitalize">{selectedListing.condition}</p>
                </div>
                <div>
                  <span className="text-sm text-gray-500">Category</span>
                  <p className="font-medium">{selectedListing.category}</p>
                </div>
                <div>
                  <span className="text-sm text-gray-500">Location</span>
                  <p className="font-medium">{selectedListing.location.name}</p>
                </div>
                <div>
                  <span className="text-sm text-gray-500">Posted</span>
                  <p className="font-medium">
                    {formatDistanceToNow(selectedListing.createdAt, { addSuffix: true })}
                  </p>
                </div>
              </div>

              {/* Delivery Options */}
              <div>
                <h3 className="font-semibold mb-2">Delivery Options</h3>
                <div className="flex flex-wrap gap-2">
                  {selectedListing.deliveryOptions.map(option => (
                    <Badge key={option} variant="secondary">
                      {option === 'pickup' ? <MapPin className="w-3 h-3 mr-1" /> : <Truck className="w-3 h-3 mr-1" />}
                      {option}
                    </Badge>
                  ))}
                </div>
              </div>

              {/* Contact Buttons */}
              <div className="space-y-3">
                <Button 
                  className="w-full" 
                  size="lg"
                  onClick={() => handleContactSeller(selectedListing, 'message')}
                >
                  <MessageCircle className="w-4 h-4 mr-2" />
                  Message Seller
                </Button>
                
                <div className="grid grid-cols-2 gap-2">
                  {selectedListing.contactInfo.phone && (
                    <Button 
                      variant="outline"
                      onClick={() => handleContactSeller(selectedListing, 'phone')}
                    >
                      <Phone className="w-4 h-4 mr-2" />
                      Call
                    </Button>
                  )}
                  {selectedListing.contactInfo.email && (
                    <Button 
                      variant="outline"
                      onClick={() => handleContactSeller(selectedListing, 'email')}
                    >
                      Email
                    </Button>
                  )}
                </div>
              </div>

              {/* Safety Tips */}
              <Card className="bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800">
                <CardContent className="p-4">
                  <div className="flex items-start space-x-2">
                    <Shield className="w-5 h-5 text-blue-600 mt-0.5" />
                    <div>
                      <h4 className="font-semibold text-blue-900 dark:text-blue-100">Safety Tips</h4>
                      <ul className="text-sm text-blue-800 dark:text-blue-200 mt-1 space-y-1">
                        <li>• Meet in a public place</li>
                        <li>• Inspect the item before buying</li>
                        <li>• Don't send money before seeing the item</li>
                      </ul>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    );
  }, [selectedListing, savedListings, handleSaveListing, handleContactSeller]);

  if (loading) {
    return (
      <div className="p-6">
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
          {Array.from({ length: 8 }).map((_, index) => (
            <Card key={index}>
              <div className="animate-pulse">
                <div className="aspect-square bg-gray-300" />
                <div className="p-3 space-y-2">
                  <div className="h-4 bg-gray-300 rounded w-3/4" />
                  <div className="h-3 bg-gray-300 rounded w-1/2" />
                  <div className="h-3 bg-gray-300 rounded w-full" />
                </div>
              </div>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">Marketplace</h1>
        <Button onClick={() => setShowCreateListing(true)}>
          <Plus className="w-4 h-4 mr-2" />
          Sell Something
        </Button>
      </div>

      {/* Search and Filters */}
      <div className="space-y-4">
        <div className="flex space-x-4">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              placeholder="Search Marketplace"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
          <Button 
            variant="outline" 
            onClick={() => setShowFilters(!showFilters)}
            className="flex items-center space-x-2"
          >
            <SlidersHorizontal className="w-4 h-4" />
            <span>Filters</span>
          </Button>
          <div className="flex items-center space-x-2 border rounded-lg p-1">
            <Button
              variant={viewMode === 'grid' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setViewMode('grid')}
            >
              <Grid className="w-4 h-4" />
            </Button>
            <Button
              variant={viewMode === 'list' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setViewMode('list')}
            >
              <List className="w-4 h-4" />
            </Button>
          </div>
        </div>

        {/* Categories */}
        <div className="flex space-x-2 overflow-x-auto pb-2">
          {categories.map(category => (
            <Button
              key={category.id}
              variant={filters.category === category.id ? 'default' : 'outline'}
              size="sm"
              onClick={() => setFilters(prev => ({ ...prev, category: category.id }))}
              className="whitespace-nowrap"
            >
              <span className="mr-2">{category.icon}</span>
              {category.name}
            </Button>
          ))}
        </div>

        {/* Advanced Filters */}
        {showFilters && (
          <Card>
            <CardContent className="p-4">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div>
                  <Label>Condition</Label>
                  <Select value={filters.condition} onValueChange={(value) => setFilters(prev => ({ ...prev, condition: value }))}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {conditions.map(condition => (
                        <SelectItem key={condition.id} value={condition.id}>
                          {condition.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label>Price Range</Label>
                  <div className="space-y-2">
                    <Slider
                      min={0}
                      max={10000}
                      step={50}
                      value={[filters.priceMin, filters.priceMax]}
                      onValueChange={([min, max]) => setFilters(prev => ({ ...prev, priceMin: min, priceMax: max }))}
                    />
                    <div className="flex justify-between text-sm text-gray-500">
                      <span>${filters.priceMin}</span>
                      <span>${filters.priceMax}</span>
                    </div>
                  </div>
                </div>

                <div>
                  <Label>Location</Label>
                  <Input
                    placeholder="Enter location"
                    value={filters.location}
                    onChange={(e) => setFilters(prev => ({ ...prev, location: e.target.value }))}
                  />
                </div>

                <div>
                  <Label>Sort By</Label>
                  <Select value={filters.sortBy} onValueChange={(value: any) => setFilters(prev => ({ ...prev, sortBy: value }))}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="recent">Most Recent</SelectItem>
                      <SelectItem value="price_low">Price: Low to High</SelectItem>
                      <SelectItem value="price_high">Price: High to Low</SelectItem>
                      <SelectItem value="distance">Distance</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Results */}
      <div>
        <div className="flex items-center justify-between mb-4">
          <p className="text-gray-600 dark:text-gray-400">
            {filteredListings.length} {filteredListings.length === 1 ? 'result' : 'results'}
          </p>
        </div>

        <AnimatePresence>
          <div className={`grid gap-4 ${
            viewMode === 'grid' 
              ? 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4' 
              : 'grid-cols-1'
          }`}>
            {filteredListings.map(listing => renderListingCard(listing))}
          </div>
        </AnimatePresence>

        {filteredListings.length === 0 && (
          <div className="text-center py-12">
            <Package className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
              No listings found
            </h3>
            <p className="text-gray-500 dark:text-gray-400">
              Try adjusting your search or filters
            </p>
          </div>
        )}
      </div>

      {/* Listing Detail Modal */}
      {renderListingDetail()}

      {/* Create Listing Modal */}
      <Dialog open={showCreateListing} onOpenChange={setShowCreateListing}>
        <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Create New Listing</DialogTitle>
          </DialogHeader>

          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="title">Title *</Label>
                <Input
                  id="title"
                  placeholder="What are you selling?"
                  value={newListing.title}
                  onChange={(e) => setNewListing(prev => ({ ...prev, title: e.target.value }))}
                />
              </div>

              <div>
                <Label htmlFor="price">Price *</Label>
                <div className="relative">
                  <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <Input
                    id="price"
                    type="number"
                    placeholder="0.00"
                    value={newListing.price}
                    onChange={(e) => setNewListing(prev => ({ ...prev, price: e.target.value }))}
                    className="pl-10"
                  />
                </div>
              </div>
            </div>

            <div>
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                placeholder="Describe your item..."
                value={newListing.description}
                onChange={(e) => setNewListing(prev => ({ ...prev, description: e.target.value }))}
                className="min-h-[100px]"
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label>Category *</Label>
                <Select value={newListing.category} onValueChange={(value) => setNewListing(prev => ({ ...prev, category: value }))}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select category" />
                  </SelectTrigger>
                  <SelectContent>
                    {categories.filter(c => c.id !== 'all').map(category => (
                      <SelectItem key={category.id} value={category.id}>
                        {category.icon} {category.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label>Condition</Label>
                <Select value={newListing.condition} onValueChange={(value) => setNewListing(prev => ({ ...prev, condition: value }))}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {conditions.filter(c => c.id !== 'all').map(condition => (
                      <SelectItem key={condition.id} value={condition.id}>
                        {condition.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div>
              <Label>Photos</Label>
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                <Camera className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                <p className="text-gray-500">Click to add photos</p>
                <p className="text-xs text-gray-400 mt-1">Add up to 10 photos</p>
              </div>
            </div>

            <div>
              <Label>Contact Information</Label>
              <div className="space-y-3">
                <div className="flex items-center space-x-2">
                  <Switch
                    checked={newListing.contactInfo.messenger}
                    onCheckedChange={(checked) => setNewListing(prev => ({
                      ...prev,
                      contactInfo: { ...prev.contactInfo, messenger: checked }
                    }))}
                  />
                  <Label>Allow messages on Messenger</Label>
                </div>
                
                <Input
                  placeholder="Phone number (optional)"
                  value={newListing.contactInfo.phone}
                  onChange={(e) => setNewListing(prev => ({
                    ...prev,
                    contactInfo: { ...prev.contactInfo, phone: e.target.value }
                  }))}
                />
                
                <Input
                  placeholder="Email (optional)"
                  value={newListing.contactInfo.email}
                  onChange={(e) => setNewListing(prev => ({
                    ...prev,
                    contactInfo: { ...prev.contactInfo, email: e.target.value }
                  }))}
                />
              </div>
            </div>

            <div className="flex justify-end space-x-2">
              <Button variant="outline" onClick={() => setShowCreateListing(false)}>
                Cancel
              </Button>
              <Button onClick={handleCreateListing}>
                Create Listing
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default FacebookMarketplace;
